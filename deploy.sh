#!/bin/bash

# 部署脚本 - 用于手动部署到目标服务器
# 使用方法: ./deploy.sh [IMAGE_TAG] [IMAGE_REPO]

set -e

# 默认值
DEFAULT_IMAGE_TAG="latest"
DEFAULT_IMAGE_REPO="registry.git.tongji.edu.cn/nic-sde/robot"
DEFAULT_DEPLOY_HOST="***************"
DEFAULT_DEPLOY_PORT="10022"
DEFAULT_DEPLOY_USER="root"
DEFAULT_APP_DIR="/usr/app"

# 获取参数
IMAGE_TAG=${1:-$DEFAULT_IMAGE_TAG}
IMAGE_REPO=${2:-$DEFAULT_IMAGE_REPO}
DEPLOY_HOST=${DEPLOY_HOST:-$DEFAULT_DEPLOY_HOST}
DEPLOY_PORT=${DEPLOY_PORT:-$DEFAULT_DEPLOY_PORT}
DEPLOY_USER=${DEPLOY_USER:-$DEFAULT_DEPLOY_USER}
APP_DIR=${APP_DIR:-$DEFAULT_APP_DIR}

echo "=========================================="
echo "开始部署机器人应用"
echo "=========================================="
echo "镜像仓库: $IMAGE_REPO"
echo "镜像标签: $IMAGE_TAG"
echo "目标服务器: $DEPLOY_USER@$DEPLOY_HOST:$DEPLOY_PORT"
echo "应用目录: $APP_DIR"
echo "=========================================="

# 检查必要的环境变量
if [ -z "$DEPLOY_MACHINE_PWD" ]; then
    echo "错误: 请设置环境变量 DEPLOY_MACHINE_PWD"
    exit 1
fi

# 创建临时目录
TEMP_DIR=$(mktemp -d)
echo "创建临时目录: $TEMP_DIR"

# 复制必要文件到临时目录
cp docker-compose.yml "$TEMP_DIR/"
cp Dockerfile "$TEMP_DIR/"

# 创建环境变量文件
cat > "$TEMP_DIR/.env" << EOF
TAG=$IMAGE_TAG
IMAGE_REPO=$IMAGE_REPO
EOF

echo "准备部署文件..."

# 上传文件到目标服务器
echo "上传文件到目标服务器..."
sshpass -p "$DEPLOY_MACHINE_PWD" -- scp -P $DEPLOY_PORT -o StrictHostKeyChecking=no \
    "$TEMP_DIR/docker-compose.yml" \
    "$TEMP_DIR/.env" \
    $DEPLOY_USER@$DEPLOY_HOST:$APP_DIR/

# 在目标服务器上执行部署
echo "在目标服务器上执行部署..."
sshpass -e -- ssh -p $DEPLOY_PORT -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST "
    echo '切换到应用目录...';
    cd $APP_DIR;
    
    echo '登录Docker仓库...';
    if [ -n \"\$CI_REGISTRY_PASSWORD\" ] && [ -n \"\$CI_REGISTRY_USER\" ] && [ -n \"\$CI_REGISTRY\" ]; then
        echo \"\$CI_REGISTRY_PASSWORD\" | docker login \$CI_REGISTRY -u \$CI_REGISTRY_USER --password-stdin;
    else
        echo '警告: 未设置Docker仓库认证信息，可能无法拉取私有镜像';
    fi;
    
    echo '拉取最新镜像...';
    docker-compose pull robot-app;
    
    echo '停止现有容器...';
    docker-compose down robot-app || true;
    
    echo '启动新容器...';
    docker-compose up -d robot-app;
    
    echo '清理旧镜像...';
    docker image prune -af --filter \"until=24h\";
    
    echo '检查容器状态...';
    docker-compose ps;
    
    echo '显示容器日志...';
    docker-compose logs --tail=50 robot-app;
"

# 清理临时目录
rm -rf "$TEMP_DIR"

echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo "应用状态:"
sshpass -e -- ssh -p $DEPLOY_PORT -o StrictHostKeyChecking=no $DEPLOY_USER@$DEPLOY_HOST "cd $APP_DIR && docker-compose ps"
