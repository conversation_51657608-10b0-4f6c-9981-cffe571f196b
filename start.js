// 生产环境启动脚本
const { spawn } = require('child_process');
const path = require('path');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.WECHATY_PUPPET_SERVICE_AUTHORITY = process.env.WECHATY_PUPPET_SERVICE_AUTHORITY || 'token-service-discovery-test.juzibot.com';
process.env.WECHATY_LOG = process.env.WECHATY_LOG || 'verbose';

// 检查是否存在编译后的文件
const fs = require('fs');
const compiledFile = path.join(__dirname, 'dist', 'src', 'index.js');
const sourceFile = path.join(__dirname, 'src', 'index.ts');

let command, args;

if (fs.existsSync(compiledFile)) {
  // 使用编译后的 JavaScript 文件
  console.log('使用编译后的 JavaScript 文件启动...');
  command = 'node';
  args = [compiledFile];
} else {
  // 使用 ts-node 直接运行 TypeScript
  console.log('使用 ts-node 启动 TypeScript 文件...');
  command = 'node';
  args = [
    '--no-warnings',
    '--loader=ts-node/esm',
    sourceFile
  ];
}

// 启动应用
const child = spawn(command, args, {
  stdio: 'inherit',
  env: process.env
});

child.on('error', (error) => {
  console.error('启动失败:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(`应用退出，退出码: ${code}`);
  process.exit(code);
});

// 处理进程信号
process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，正在关闭...');
  child.kill('SIGTERM');
});

process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，正在关闭...');
  child.kill('SIGINT');
});
