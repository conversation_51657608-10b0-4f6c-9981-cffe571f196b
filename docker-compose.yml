version: '3.8'

services:
  robot-app:
    image: ${IMAGE_REPO}:${TAG}
    container_name: robot-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com
      # 添加其他必要的环境变量
      - WECHATY_LOG=verbose
    volumes:
      # 如果需要持久化数据，可以挂载卷
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - robot-network
    # 如果需要暴露端口
    # ports:
    #   - "3000:3000"
    # 健康检查
    healthcheck:
      test: ["CMD", "node", "--version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  robot-network:
    driver: bridge
