# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output/

# 依赖目录
.npm
.eslintcache

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出目录
out/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.gitlab-ci.yml
.github/

# 文档
*.md
!README.md

# 测试文件
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 临时文件
tmp/
temp/
