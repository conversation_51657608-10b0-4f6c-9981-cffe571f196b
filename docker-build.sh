#!/bin/bash

# Docker 构建优化脚本
# 使用方法: ./docker-build.sh [TAG]

set -e

# 默认标签
DEFAULT_TAG="latest"
TAG=${1:-$DEFAULT_TAG}
IMAGE_NAME="tongji-robot"

echo "==========================================="
echo "Docker 构建优化脚本"
echo "==========================================="
echo "镜像名称: $IMAGE_NAME"
echo "标签: $TAG"
echo "==========================================="

# 启用 BuildKit 以获得更好的构建性能
export DOCKER_BUILDKIT=1

# 构建镜像
echo "开始构建镜像..."
docker build \
  --tag $IMAGE_NAME:$TAG \
  --tag $IMAGE_NAME:latest \
  --build-arg BUILDKIT_INLINE_CACHE=1 \
  --progress=plain \
  .

echo "==========================================="
echo "构建完成！"
echo "镜像: $IMAGE_NAME:$TAG"
echo "==========================================="

# 显示镜像信息
docker images | grep $IMAGE_NAME

echo "==========================================="
echo "运行测试命令:"
echo "docker run --rm $IMAGE_NAME:$TAG node --version"
echo "==========================================="
