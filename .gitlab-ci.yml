variables:
  IMAGE_TAG: '${CI_PIPELINE_ID}-${CI_COMMIT_REF_NAME}'

stages:
  - build
  - deploy

build:
  stage: build
  image: docker:20.10
  services:
    - docker:20.10-dind
  before_script:
    - docker info
    - echo $CI_REGISTRY_PASSWORD | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin
  script:
    - echo $IMAGE_TAG
    - docker build -t $CI_REGISTRY_IMAGE:$IMAGE_TAG .
    - docker push $CI_REGISTRY_IMAGE:$IMAGE_TAG
  tags:
    - unicom-docker3
  only:
    - main

deploy:
  stage: deploy
  needs: ['build']
  image: ringcentral/sshpass
  before_script:
    - echo "Testing SSH connection..."
    - sshpass -p "$SSH_PWD" ssh -p 10022 -o StrictHostKeyChecking=no -o ConnectTimeout=10 root@*************** "echo 'SSH connection successful'"
    - echo "Copying docker-compose.yml..."
    - sshpass -p "$SSH_PWD" scp -P 10022 -o StrictHostKeyChecking=no docker-compose.yml root@***************:/usr/app/docker-compose.yml
  script:
    - export SSHPASS=$SSH_PWD
    - echo "Starting deployment..."
    - sshpass -e ssh -p 10022 -o StrictHostKeyChecking=no -o ConnectTimeout=30 root@*************** "echo 'Logging into Docker registry...'; echo \"$CI_REGISTRY_PASSWORD\" | docker login $CI_REGISTRY -u $CI_REGISTRY_USER --password-stdin; cd /usr/app; echo \"TAG=$IMAGE_TAG\" > .env; echo \"IMAGE_REPO=$CI_REGISTRY_IMAGE\" >> .env; echo 'Pulling latest image...'; docker-compose pull robot-app; echo 'Starting new containers...'; docker-compose up -d robot-app; echo 'Deployment completed successfully!'"
  tags:
    - unicom-docker3
  only:
    - main
  when: manual
