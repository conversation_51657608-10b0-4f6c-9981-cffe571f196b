@echo off
REM 部署脚本 - 用于手动部署到目标服务器
REM 使用方法: deploy.bat [IMAGE_TAG] [IMAGE_REPO]

setlocal enabledelayedexpansion

REM 默认值
set DEFAULT_IMAGE_TAG=latest
set DEFAULT_IMAGE_REPO=registry.git.tongji.edu.cn/nic-sde/robot
set DEFAULT_DEPLOY_HOST=***************
set DEFAULT_DEPLOY_PORT=10022
set DEFAULT_DEPLOY_USER=root
set DEFAULT_APP_DIR=/usr/app

REM 获取参数
if "%1"=="" (
    set IMAGE_TAG=%DEFAULT_IMAGE_TAG%
) else (
    set IMAGE_TAG=%1
)

if "%2"=="" (
    set IMAGE_REPO=%DEFAULT_IMAGE_REPO%
) else (
    set IMAGE_REPO=%2
)

set DEPLOY_HOST=%DEFAULT_DEPLOY_HOST%
set DEPLOY_PORT=%DEFAULT_DEPLOY_PORT%
set DEPLOY_USER=%DEFAULT_DEPLOY_USER%
set APP_DIR=%DEFAULT_APP_DIR%

echo ==========================================
echo 开始部署机器人应用
echo ==========================================
echo 镜像仓库: %IMAGE_REPO%
echo 镜像标签: %IMAGE_TAG%
echo 目标服务器: %DEPLOY_USER%@%DEPLOY_HOST%:%DEPLOY_PORT%
echo 应用目录: %APP_DIR%
echo ==========================================

REM 检查必要的环境变量
if "%DEPLOY_MACHINE_PWD%"=="" (
    echo 错误: 请设置环境变量 DEPLOY_MACHINE_PWD
    exit /b 1
)

REM 创建临时目录
set TEMP_DIR=%TEMP%\robot-deploy-%RANDOM%
mkdir "%TEMP_DIR%"
echo 创建临时目录: %TEMP_DIR%

REM 复制必要文件到临时目录
copy docker-compose.yml "%TEMP_DIR%\"
copy Dockerfile "%TEMP_DIR%\"

REM 创建环境变量文件
echo TAG=%IMAGE_TAG% > "%TEMP_DIR%\.env"
echo IMAGE_REPO=%IMAGE_REPO% >> "%TEMP_DIR%\.env"

echo 准备部署文件...

REM 上传文件到目标服务器
echo 上传文件到目标服务器...
sshpass -p "%DEPLOY_MACHINE_PWD%" -- scp -P %DEPLOY_PORT% -o StrictHostKeyChecking=no "%TEMP_DIR%\docker-compose.yml" %DEPLOY_USER%@%DEPLOY_HOST%:%APP_DIR%/
sshpass -p "%DEPLOY_MACHINE_PWD%" -- scp -P %DEPLOY_PORT% -o StrictHostKeyChecking=no "%TEMP_DIR%\.env" %DEPLOY_USER%@%DEPLOY_HOST%:%APP_DIR%/

REM 在目标服务器上执行部署
echo 在目标服务器上执行部署...
sshpass -e -- ssh -p %DEPLOY_PORT% -o StrictHostKeyChecking=no %DEPLOY_USER%@%DEPLOY_HOST% "cd %APP_DIR% && docker-compose pull robot-app && docker-compose down robot-app || true && docker-compose up -d robot-app && docker image prune -af --filter \"until=24h\" && docker-compose ps"

REM 清理临时目录
rmdir /s /q "%TEMP_DIR%"

echo ==========================================
echo 部署完成！
echo ==========================================
