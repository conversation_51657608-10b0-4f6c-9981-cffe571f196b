# 同济同学企业微信机器人 - 管理员开发说明书

## 项目概述

同济同学企业微信机器人是基于 Wechaty 框架开发的企业微信智能对话机器人，采用 TypeScript 开发，支持多智能体架构、持久化存储、权限管理等企业级功能。

## 系统架构

### 技术栈

- **框架**: Wechaty (企业微信集成)
- **语言**: TypeScript/Node.js
- **数据库**: MySQL (主数据存储) + Redis (缓存和会话管理)
- **容器化**: Docker
- **流式处理**: Go 服务 (可选)

### 核心模块

```
src/
├── agents/           # 智能体模块
├── config/          # 配置管理
├── dao/             # 数据访问层
├── handlers/        # 消息处理器
├── services/        # 业务服务层
└── utils/           # 工具函数
```

## 环境配置

### 必需环境变量

```bash
# Wechaty配置
WECHATY_TOKEN=puppet_workpro_xxxxx
WECHATY_PUPPET=@juzi/wechaty-puppet-service

# MySQL配置
MYSQL_HOST=YOUR_HOST
MYSQL_PORT=3306
MYSQL_USER=YOUR_USER
MYSQL_PASSWORD=YOUR_PASSWORD
MYSQL_DB=YOUR_DB
MYSQL_POOL=10

# Redis配置
REDIS_HOST=YOUR_HOST
REDIS_PORT=6379
REDIS_PASSWORD=YOUR_PASSWORD
REDIS_DB=0

# Docker环境
IS_DOCKER=true
VERIFY_CODE=verification_code
```

### 数据库初始化

```sql
-- 智能体配置表
CREATE TABLE `agent`  (
  `appid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体appid',
  `id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体名称',
  `appkey` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体访问密钥',
  `base_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '智能体调用的基础URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`appid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '智能体表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of agent
-- ----------------------------
INSERT INTO `agent` VALUES ('d24qen3m1orcf178pn4g', 'info', '信息办助手', 'd319dblnmn9rlsg0oibg', 'https://agent.tongji.edu.cn/api/proxy/api/v1', '信息办助手', '2025-09-15 16:23:43', '2025-09-15 16:23:43');
INSERT INTO `agent` VALUES ('tjtx', 'tjtx', '同济同学', 'cvvgdkuh36688d49d5fg', 'https://agent.tongji.edu.cn/api/proxy/api/v1', '同济同学', '2025-09-15 16:22:40', '2025-09-15 16:57:57');

SET FOREIGN_KEY_CHECKS = 1;

-- 群聊配置表
CREATE TABLE `group_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '群聊ID',
  `room_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '群聊名称',
  `agent_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认绑定的智能体ID',
  `allow_agent_switch` tinyint(1) NULL DEFAULT 0 COMMENT '是否允许切换智能体（0=否，1=是）',
  `enable_push` tinyint(1) NULL DEFAULT 1 COMMENT '是否开启推送（0=否，1=是）',
  `enable_blacklist` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启黑名单（0=否，1=是）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_room_id`(`room_id` ASC) USING BTREE,
  INDEX `idx_agent`(`agent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '群聊配置表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;


-- 对话记录表
CREATE TABLE `conversation_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `conversation_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '会话ID（群聊或私聊唯一标识）',
  `conversation_type` enum('private','group') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'private' COMMENT '会话类型：private=私聊，group=群聊',
  `room_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '群聊ID（仅群聊有值）',
  `room_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '群聊名称（仅群聊有值）',
  `user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发送者用户ID',
  `user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发送者昵称',
  `role` enum('user','assistant','system') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消息角色：用户/AI/系统',
  `message_type` enum('text','image','audio','video','file','event') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '消息内容（文本消息）',
  `content_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '多媒体资源地址（图片/音频/文件等）',
  `metadata` json NULL COMMENT '扩展信息（AI参数、tokens、模型配置、上下文摘要等）',
  `model` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '使用的模型',
  `status_code` int NULL DEFAULT NULL COMMENT '请求状态码',
  `duration_ms` int NULL DEFAULT NULL COMMENT '耗时（毫秒）',
  `parent_message_id` bigint NULL DEFAULT NULL COMMENT '父消息ID（引用/回复/上下文追踪）',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '调用链追踪ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户IP（IPv4/IPv6）',
  `device_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备信息（浏览器UA/客户端等）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '消息时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_conv_time`(`conversation_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_user_time`(`user_id` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_room_time`(`room_id` ASC, `created_at` ASC) USING BTREE,
  FULLTEXT INDEX `ft_content`(`content`)
) ENGINE = InnoDB AUTO_INCREMENT = 185 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会话记录表' ROW_FORMAT = Dynamic;
```

## 智能体管理

### 智能体架构

每个智能体包含以下文件：

```
src/agents/{agent_name}/
├── config.ts    # 智能体配置
├── agent.ts     # 智能体实现
└── api.ts       # API调用逻辑
```

### 添加新智能体

1. **创建配置文件** (`config.ts`)

```typescript
export const NEW_AGENT_CONFIG = {
  id: 'new_agent',
  name: '新智能体',
  kind: 'api-type' as const,
  description: '智能体描述',
  api: {
    baseUrl: 'https://api.example.com',
    apiKey: 'your_api_key',
    userId: 'user_id'
  }
}
```

2. **实现智能体** (`agent.ts`)

```typescript
import { Agent } from '../types.js'
import { NEW_AGENT_CONFIG } from './config.js'

export const newAgent: Agent = {
  id: NEW_AGENT_CONFIG.id,
  name: NEW_AGENT_CONFIG.name,
  kind: NEW_AGENT_CONFIG.kind,
  description: NEW_AGENT_CONFIG.description,
  respond: async (query, ctx) => {
    // 实现对话逻辑
    const { room, talker, message } = ctx
    // 调用API并返回结果
    return 'AI回复内容'
  }
}
```

3. **实现 API 调用** (`api.ts`)

```typescript
export async function createConversation(sessionKey: string): Promise<string | null> {
  // 创建会话逻辑
}

export async function* chatQueryStreaming(conversationId: string, query: string) {
  // 流式对话逻辑
}
```

4. **注册智能体** 在数据库中添加智能体记录：

```sql
INSERT INTO agents (id, name, description, base_url, appid, appkey)
VALUES ('new_agent', '新智能体', '描述', 'https://api.example.com', 'app_id', 'api_key');
```

### 智能体配置管理

智能体配置支持数据库动态配置，配置变更实时生效：

**Redis 存储结构**：

- `agent:config:{agentId}` - 智能体配置缓存
- `agent:setting:{sessionKey}` - 会话智能体设置

## 消息处理流程

### 消息处理架构

```
消息接收 → 类型判断 → 权限检查 → 命令解析 → 智能体路由 → 响应生成
```

### 消息类型处理

1. **文本消息**

   - 命令解析 (`parseAgentCommand`)
   - 关键词检查 (`checkAndHandleKeyword`)
   - AI 对话处理 (`handleAIResponse`)

2. **多媒体消息**
   - 图片：`handleGroupImage` / `handlePrivateImage`
   - 语音：`handleGroupAudio` / `handlePrivateAudio`
   - 视频：`handleGroupVideo` / `handlePrivateVideo`
   - 文件：`handleGroupFile` / `handlePrivateFile`

### 权限管理

```typescript
// 检查智能体命令权限
const hasPermission = await PermissionManager.canExecuteAgentCommand(room, talker)

// 权限级别
- 群聊：群主/管理员可执行智能体命令
- 私聊：默认拥有所有权限
```

## 数据持久化

### Redis 缓存策略

```typescript
// 存储结构
{
  "agent:setting:{sessionKey}": "agentId",           // 智能体设置
  "room:info:{roomId}": "roomInfo",                  // 群聊信息
  "agent:config:{agentId}": "agentConfig",           // 智能体配置
  "user:info:{pid}": "userInfo",                     // 用户信息缓存
  "conversation:{sessionKey}": "conversationId"      // 会话ID
}
```

### 缓存过期策略

- 用户信息：24 小时
- 群聊信息：实时更新
- 智能体设置：永久保存
- 会话信息：根据活跃度动态管理

## 学工信息查询

### API 集成

```typescript
// 同济学工API
const API_URL = 'https://api.tongji.edu.cn/v2/dc/user/person_info_by_pid'

// 支持的查询关键词
const KEYWORDS = ['学号', '学工号', '工号', '手机号', '部门', '学院', '姓名']
```

### 查询流程

1. 关键词检测
2. PID 提取（从消息文本或用户信息）
3. API 调用
4. 结果缓存
5. 格式化输出

## 会话管理

### 会话状态管理

```typescript
// 会话状态
interface ConversationState {
  isActive: boolean
  lastActivity: number
  sessionKey: string
}

// 状态操作
activateConversation(sessionKey) // 激活会话
endConversation(sessionKey) // 结束会话
isConversationActive(sessionKey) // 检查会话状态
updateLastActivity(sessionKey) // 更新活动时间
```

### 连续对话逻辑

- 群聊：@机器人后激活会话，支持短时间内连续对话
- 私聊：默认支持连续对话
- 超时机制：会话超时自动结束

## 部署指南

### 部署架构概述

项目支持多种部署方式：

- **生产环境**: 使用 GitLab CI/CD 自动化部署
- **开发环境**: 本地 Docker 部署
- **手动部署**: 使用部署脚本

### 生产环境部署（推荐）

#### 1. GitLab CI/CD 自动部署

**前置条件**：

- GitLab Runner 已配置
- 目标服务器已准备就绪
- 环境变量已配置

**部署流程**：

1. 推送代码到 `main` 分支触发构建
2. 在 GitLab CI/CD 界面手动触发部署阶段
3. 系统自动部署到目标服务器 (***************:10022)

**GitLab 环境变量配置**：

```bash
# Docker 仓库认证
CI_REGISTRY_PASSWORD=<your-registry-password>
CI_REGISTRY_USER=<your-registry-username>
CI_REGISTRY=registry.git.tongji.edu.cn

# 部署服务器认证
DEPLOY_MACHINE_PWD=<deploy-server-password>
```

#### 2. 目标服务器配置

**服务器信息**：

- IP: ***************
- 端口: 10022
- 用户: root
- 应用目录: /usr/app

**服务器准备**：

```bash
# 1. 安装 Docker 和 Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 2. 创建应用目录
mkdir -p /usr/app/{logs,data}
cd /usr/app

# 3. 配置 SSH 服务（如果需要）
sudo systemctl enable ssh
sudo systemctl start ssh
```

**目录结构**：

```
/usr/app/
├── docker-compose.yml    # 容器编排配置
├── .env                 # 环境变量文件
├── logs/               # 应用日志目录
└── data/               # 应用数据目录
```

### 手动部署

#### Linux/macOS 部署

```bash
# 1. 设置环境变量
export DEPLOY_MACHINE_PWD="your-password"
export CI_REGISTRY_PASSWORD="your-registry-password"
export CI_REGISTRY_USER="your-registry-username"
export CI_REGISTRY="registry.git.tongji.edu.cn"

# 2. 执行部署脚本
./deploy.sh [IMAGE_TAG] [IMAGE_REPO]

# 示例
./deploy.sh v1.0.0 registry.git.tongji.edu.cn/nic-sde/robot
```

#### Windows 部署

```cmd
REM 1. 设置环境变量
set DEPLOY_MACHINE_PWD=your-password
set CI_REGISTRY_PASSWORD=your-registry-password
set CI_REGISTRY_USER=your-registry-username
set CI_REGISTRY=registry.git.tongji.edu.cn

REM 2. 执行部署脚本
deploy.bat [IMAGE_TAG] [IMAGE_REPO]

REM 示例
deploy.bat v1.0.0 registry.git.tongji.edu.cn/nic-sde/robot
```

### 本地开发部署

#### Docker 部署

```bash
# 1. 构建镜像
docker build -t tongji-bot .

# 2. 创建环境变量文件
cat > .env << EOF
MYSQL_HOST=your_mysql_host
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_DB=your_mysql_db
REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
WECHATY_TOKEN=your_wechaty_token
IS_DOCKER=true
EOF

# 3. 运行容器
docker run -d \
  --name tongji-bot \
  --env-file .env \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  tongji-bot
```

#### Docker Compose 部署

```bash
# 1. 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置相关参数

# 2. 启动服务
docker-compose up -d

# 3. 查看日志
docker-compose logs -f robot-app
```

#### 本地环境部署

```bash
# 1. 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 4. 编译 TypeScript
npm run build

# 5. 启动服务
npm start
```

### Go 流式服务部署

Go 服务提供流式对话功能，可独立部署：

```bash
# 1. 进入 Go 目录
cd go/

# 2. Docker 部署
docker build -t tongji-go-service .
docker run -d \
  --name tongji-go-service \
  -p 8084:8080 \
  tongji-go-service

# 3. 本地部署
go mod tidy
go build -o main .
./main

# 服务将在 http://localhost:8080 启动
```

## 监控与维护

### 服务状态监控

#### 查看容器状态

```bash
# 在目标服务器上执行
cd /usr/app

# 查看所有容器状态
docker-compose ps

# 查看特定服务状态
docker-compose ps robot-app

# 查看容器详细信息
docker inspect robot-app
```

#### 查看应用日志

```bash
# 查看实时日志
docker-compose logs -f robot-app

# 查看最近50行日志
docker-compose logs --tail=50 robot-app

# 查看特定时间段日志
docker-compose logs --since="2024-01-01T00:00:00" --until="2024-01-01T23:59:59" robot-app

# 保存日志到文件
docker-compose logs robot-app > robot-app.log
```

#### 系统资源监控

```bash
# 查看容器资源使用情况
docker stats robot-app

# 查看系统资源
htop
free -h
df -h

# 查看网络连接
netstat -tulpn | grep :3000
```

### 服务管理操作

#### 重启服务

```bash
# 重启特定服务
docker-compose restart robot-app

# 重新部署服务
docker-compose down robot-app
docker-compose up -d robot-app

# 强制重新创建容器
docker-compose up -d --force-recreate robot-app
```

#### 更新服务

```bash
# 拉取最新镜像
docker-compose pull robot-app

# 停止服务并更新
docker-compose down robot-app
docker-compose up -d robot-app
```

#### 备份与恢复

```bash
# 备份数据目录
tar -czf robot-backup-$(date +%Y%m%d).tar.gz /usr/app/data /usr/app/logs

# 备份数据库（如果需要）
mysqldump -h mysql.tongji.edu.cn -u username -p database_name > backup.sql

# 恢复数据
tar -xzf robot-backup-20240101.tar.gz -C /
```

### 日志记录系统

#### 应用日志类型

- **用户消息记录**: `logUserMessage` - 记录用户发送的消息
- **机器人回复记录**: `logAssistantText` - 记录 AI 回复内容
- **多媒体消息记录**: `logMultimediaMessage` - 记录图片、语音等
- **系统事件日志**: 记录系统启动、错误、警告等
- **API 调用日志**: 记录外部 API 调用情况

#### 日志文件位置

```bash
# 容器内日志
/app/logs/
├── app.log          # 应用主日志
├── error.log        # 错误日志
├── access.log       # 访问日志
└── debug.log        # 调试日志

# 宿主机日志映射
/usr/app/logs/       # 对应容器内 /app/logs/
```

#### 日志轮转配置

```bash
# 配置 logrotate
sudo tee /etc/logrotate.d/robot-app << EOF
/usr/app/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 nodejs nodejs
}
EOF
```

### 性能监控

#### 关键指标监控

- **API 响应时间**: 监控智能体 API 调用延迟
- **数据库连接池状态**: 监控 MySQL 连接池使用情况
- **Redis 连接状态**: 监控缓存服务可用性
- **内存使用情况**: 监控 Node.js 进程内存占用
- **消息处理速度**: 监控消息队列处理效率

#### 监控脚本示例

```bash
#!/bin/bash
# 健康检查脚本 health-check.sh

# 检查容器状态
if ! docker-compose ps robot-app | grep -q "Up"; then
    echo "ERROR: robot-app container is not running"
    exit 1
fi

# 检查应用端口
if ! nc -z localhost 3000; then
    echo "ERROR: Application port 3000 is not accessible"
    exit 1
fi

# 检查数据库连接
if ! mysqladmin ping -h mysql.tongji.edu.cn -u username -p password; then
    echo "ERROR: Database connection failed"
    exit 1
fi

# 检查Redis连接
if ! redis-cli -h redis.host ping; then
    echo "ERROR: Redis connection failed"
    exit 1
fi

echo "All health checks passed"
```

### 故障排除

#### 常见问题及解决方案

**1. 容器启动失败**

```bash
# 查看容器启动日志
docker-compose logs robot-app

# 检查配置文件
docker-compose config

# 检查环境变量
docker-compose exec robot-app env

# 重新构建镜像
docker-compose build --no-cache robot-app
```

**2. 数据库连接失败**

```bash
# 测试数据库连接
mysql -h mysql.tongji.edu.cn -u username -p

# 检查网络连接
ping mysql.tongji.edu.cn
telnet mysql.tongji.edu.cn 3306

# 检查环境变量配置
echo $MYSQL_HOST $MYSQL_USER $MYSQL_PASSWORD
```

**3. Redis 连接问题**

```bash
# 测试Redis连接
redis-cli -h redis.host -p 6379 ping

# 检查Redis服务状态
systemctl status redis

# 清理Redis缓存
redis-cli -h redis.host FLUSHALL
```

**4. 内存不足**

```bash
# 查看内存使用
free -h
docker stats

# 清理Docker资源
docker system prune -af
docker volume prune -f

# 重启服务释放内存
docker-compose restart robot-app
```

**5. 磁盘空间不足**

```bash
# 查看磁盘使用
df -h

# 清理日志文件
find /usr/app/logs -name "*.log" -mtime +7 -delete

# 清理Docker镜像
docker image prune -af --filter "until=24h"
```

#### 紧急恢复流程

```bash
# 1. 停止所有服务
docker-compose down

# 2. 备份当前状态
cp -r /usr/app /usr/app.backup.$(date +%Y%m%d_%H%M%S)

# 3. 恢复到上一个稳定版本
docker-compose pull robot-app:stable
docker-compose up -d robot-app

# 4. 验证服务状态
docker-compose ps
docker-compose logs --tail=50 robot-app
```

### 维护计划

#### 定期维护任务

**每日任务**:

- 检查服务运行状态
- 查看错误日志
- 监控资源使用情况

**每周任务**:

- 清理旧日志文件
- 更新系统补丁
- 备份重要数据

**每月任务**:

- 清理 Docker 镜像和容器
- 数据库性能优化
- 安全漏洞扫描

#### 自动化维护脚本

```bash
#!/bin/bash
# 自动维护脚本 maintenance.sh

echo "Starting maintenance tasks..."

# 清理旧日志
find /usr/app/logs -name "*.log" -mtime +30 -delete

# 清理Docker资源
docker system prune -af --filter "until=72h"

# 重启服务（如果需要）
if [ "$1" = "--restart" ]; then
    docker-compose restart robot-app
fi

# 健康检查
./health-check.sh

echo "Maintenance completed"
```

### 安全注意事项

#### 访问控制

- 限制 SSH 访问 IP 范围
- 使用密钥认证替代密码认证
- 定期更换密码和密钥
- 配置防火墙规则

#### 数据安全

- 定期备份数据库和配置文件
- 加密敏感配置信息
- 监控异常访问行为
- 及时更新安全补丁

#### 网络安全

```bash
# 配置防火墙
sudo ufw enable
sudo ufw allow 10022/tcp  # SSH
sudo ufw allow 3000/tcp   # 应用端口（如果需要）

# 限制访问来源
sudo ufw allow from ***********/16 to any port 3000
```

## 快速开始指南

### 新环境部署清单

#### 1. 服务器准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要工具
sudo apt install -y curl wget git htop net-tools

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

#### 2. 创建部署目录

```bash
# 创建应用目录
sudo mkdir -p /usr/app/{logs,data}
cd /usr/app

# 设置权限
sudo chown -R $USER:$USER /usr/app
```

#### 3. 配置环境变量

创建 `.env` 文件：

```bash
cat > /usr/app/.env << 'EOF'
# 镜像配置
TAG=latest
IMAGE_REPO=registry.git.tongji.edu.cn/nic-sde/robot

# Node.js 环境
NODE_ENV=production

# Wechaty 配置
WECHATY_TOKEN=puppet_workpro_xxxxx
WECHATY_PUPPET=@juzi/wechaty-puppet-service
WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com
WECHATY_LOG=verbose

# MySQL 配置
MYSQL_HOST=mysql.tongji.edu.cn
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_DB=your_database
MYSQL_POOL=10

# Redis 配置
REDIS_HOST=your_redis_host
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# Docker 环境标识
IS_DOCKER=true
VERIFY_CODE=your_verification_code
EOF
```

#### 4. 创建 docker-compose.yml

```bash
cat > /usr/app/docker-compose.yml << 'EOF'
version: '3.8'

services:
  robot-app:
    image: ${IMAGE_REPO}:${TAG}
    container_name: robot-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - WECHATY_TOKEN=${WECHATY_TOKEN}
      - WECHATY_PUPPET=${WECHATY_PUPPET}
      - WECHATY_PUPPET_SERVICE_AUTHORITY=${WECHATY_PUPPET_SERVICE_AUTHORITY}
      - WECHATY_LOG=${WECHATY_LOG}
      - MYSQL_HOST=${MYSQL_HOST}
      - MYSQL_PORT=${MYSQL_PORT}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_DB=${MYSQL_DB}
      - MYSQL_POOL=${MYSQL_POOL}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DB=${REDIS_DB}
      - IS_DOCKER=${IS_DOCKER}
      - VERIFY_CODE=${VERIFY_CODE}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    networks:
      - robot-network
    healthcheck:
      test: ["CMD", "node", "--version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

networks:
  robot-network:
    driver: bridge
EOF
```

#### 5. 部署服务

```bash
# 登录 Docker 仓库（如果是私有仓库）
docker login registry.git.tongji.edu.cn

# 拉取镜像
docker-compose pull

# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps
docker-compose logs -f robot-app
```

### 开发环境快速搭建

#### 1. 克隆代码

```bash
git clone https://git.tongji.edu.cn/nic-sde/robot.git
cd robot
```

#### 2. 安装依赖

```bash
# 安装 Node.js 依赖
npm install

# 安装 TypeScript 全局工具
npm install -g typescript ts-node
```

#### 3. 配置环境

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 4. 启动开发服务

```bash
# 编译 TypeScript
npm run build

# 启动服务
npm start

# 或者使用开发模式
npm run dev
```

### 常用运维命令

#### 服务管理

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f robot-app

# 重启服务
docker-compose restart robot-app

# 更新服务
docker-compose pull && docker-compose up -d

# 停止服务
docker-compose down
```

#### 数据库操作

```bash
# 连接数据库
mysql -h mysql.tongji.edu.cn -u username -p

# 备份数据库
mysqldump -h mysql.tongji.edu.cn -u username -p database_name > backup.sql

# 恢复数据库
mysql -h mysql.tongji.edu.cn -u username -p database_name < backup.sql
```

#### 系统监控

```bash
# 查看系统资源
htop
free -h
df -h

# 查看容器资源
docker stats

# 查看网络连接
netstat -tulpn | grep :3000
```

**最后更新**: 2025-09-17 **版本**: v1.0.0 **维护者**: 同济大学信息办
