# GitLab CI/CD 部署配置

本文档描述了机器人项目的GitLab CI/CD自动化部署配置。

## 部署架构

- **构建阶段**: 在GitLab Runner上构建Docker镜像并推送到镜像仓库
- **部署阶段**: 通过SSH连接到目标服务器(***************:10022)进行部署
- **部署方式**: 使用Docker Compose进行容器编排

## 文件说明

### 核心配置文件

1. **`.gitlab-ci.yml`** - GitLab CI/CD流水线配置
2. **`Dockerfile`** - Docker镜像构建文件
3. **`docker-compose.yml`** - 容器编排配置
4. **`.dockerignore`** - Docker构建忽略文件

### 部署脚本

1. **`deploy.sh`** - Linux/macOS手动部署脚本
2. **`deploy.bat`** - Windows手动部署脚本

## 环境变量配置

### GitLab CI/CD变量

在GitLab项目设置中配置以下变量：

```bash
# Docker仓库认证
CI_REGISTRY_PASSWORD=<your-registry-password>
CI_REGISTRY_USER=<your-registry-username>
CI_REGISTRY=<your-registry-url>

# 部署服务器认证
DEPLOY_MACHINE_PWD=<deploy-server-password>
```

### 应用环境变量

在`docker-compose.yml`中配置：

```yaml
environment:
  - NODE_ENV=production
  - WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com
  - WECHATY_LOG=verbose
```

## 部署流程

### 自动部署（推荐）

1. 推送代码到`main`分支
2. GitLab自动触发构建阶段
3. 构建完成后，在GitLab CI/CD界面手动触发部署阶段
4. 系统自动部署到目标服务器

### 手动部署

#### Linux/macOS

```bash
# 设置环境变量
export DEPLOY_MACHINE_PWD="your-password"

# 执行部署
./deploy.sh [IMAGE_TAG] [IMAGE_REPO]

# 示例
./deploy.sh v1.0.0 registry.git.tongji.edu.cn/nic-sde/robot
```

#### Windows

```cmd
REM 设置环境变量
set DEPLOY_MACHINE_PWD=your-password

REM 执行部署
deploy.bat [IMAGE_TAG] [IMAGE_REPO]

REM 示例
deploy.bat v1.0.0 registry.git.tongji.edu.cn/nic-sde/robot
```

## 目标服务器配置

### 服务器信息

- **IP地址**: ***************
- **端口**: 10022
- **用户**: root
- **应用目录**: /usr/app

### 服务器准备

1. 安装Docker和Docker Compose
2. 确保SSH服务运行在10022端口
3. 创建应用目录：`mkdir -p /usr/app`
4. 配置SSH密钥认证（可选，推荐）

### 目录结构

```
/usr/app/
├── docker-compose.yml
├── .env
└── logs/          # 应用日志目录
└── data/          # 应用数据目录
```

## 监控和维护

### 查看容器状态

```bash
# 在目标服务器上执行
cd /usr/app
docker-compose ps
```

### 查看应用日志

```bash
# 查看实时日志
docker-compose logs -f robot-app

# 查看最近50行日志
docker-compose logs --tail=50 robot-app
```

### 重启应用

```bash
# 重启服务
docker-compose restart robot-app

# 重新部署
docker-compose down robot-app
docker-compose up -d robot-app
```

### 清理资源

```bash
# 清理未使用的镜像
docker image prune -af --filter "until=24h"

# 清理未使用的容器
docker container prune -f

# 清理未使用的网络
docker network prune -f
```

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查服务器IP和端口
   - 验证SSH服务状态
   - 确认密码正确

2. **Docker镜像拉取失败**
   - 检查镜像仓库认证
   - 验证镜像标签是否存在
   - 确认网络连接

3. **容器启动失败**
   - 查看容器日志：`docker-compose logs robot-app`
   - 检查环境变量配置
   - 验证端口占用情况

4. **应用运行异常**
   - 检查应用日志
   - 验证数据库连接
   - 确认依赖服务状态

### 日志位置

- **容器日志**: `docker-compose logs robot-app`
- **应用日志**: `/usr/app/logs/`（如果配置了卷挂载）

## 安全注意事项

1. **密码安全**: 使用强密码，定期更换
2. **SSH安全**: 建议使用密钥认证替代密码认证
3. **网络安全**: 确保防火墙配置正确
4. **镜像安全**: 定期更新基础镜像，扫描安全漏洞

## 版本管理

- 镜像标签格式：`${CI_PIPELINE_ID}-${CI_COMMIT_REF_NAME}`
- 示例：`123-main`、`124-feature-branch`
- 支持回滚到任意历史版本

## 联系支持

如有问题，请联系开发团队或查看项目文档。
