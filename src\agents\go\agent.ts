import { Agent, AgentRequestContext } from '../types.js'
import { GO_CONFIG } from './config.js'
import { chatQueryGoStreaming } from './api.js'

export const goAgent: Agent = {
  id: GO_CONFIG.id,
  name: GO_CONFIG.name,
  kind: GO_CONFIG.kind,
  description: GO_CONFIG.description,
  respond: async (query, ctx) => {
    const { room, talker, message } = ctx

    if (room) {
      // 群聊中需要特殊处理，因为Go流式API需要Message对象
      return await chatQueryGoStreaming(room.id, query, message, talker)
    } else {
      // 私聊处理
      return await chatQueryGoStreaming(talker.id, query, message, talker)
    }
  }
}
