FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps

WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装生产依赖
RUN npm ci --only=production --no-audit --prefer-offline && \
    npm cache clean --force

# Install dev dependencies for TypeScript
FROM base AS builder

WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装所有依赖（包括 TypeScript）
RUN npm ci --no-audit --prefer-offline

# 复制源代码
COPY tsconfig.json ./
COPY agentfunc.ts ./
COPY src/ ./src/

# TypeScript 项目不需要预编译，直接使用 ts-node 运行

# Production image
FROM base AS runner

WORKDIR /app

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 从 deps 阶段复制生产依赖
COPY --from=deps /app/node_modules ./node_modules

# 从 builder 阶段复制 TypeScript 相关依赖
COPY --from=builder /app/node_modules/typescript ./node_modules/typescript
COPY --from=builder /app/node_modules/ts-node ./node_modules/ts-node
COPY --from=builder /app/node_modules/@types ./node_modules/@types

# 复制应用文件
COPY --chown=nodejs:nodejs package*.json ./
COPY --chown=nodejs:nodejs tsconfig.json ./
COPY --chown=nodejs:nodejs agentfunc.ts ./
COPY --chown=nodejs:nodejs src/ ./src/

# 切换到非 root 用户
USER nodejs

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com
ENV HOSTNAME="0.0.0.0"

# 启动命令
CMD ["npm", "start"]
