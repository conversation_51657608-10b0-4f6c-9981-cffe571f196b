@echo off
REM Docker 构建优化脚本 (Windows)
REM 使用方法: docker-build.bat [TAG]

setlocal

REM 默认标签
set DEFAULT_TAG=latest
set TAG=%1
if "%TAG%"=="" set TAG=%DEFAULT_TAG%
set IMAGE_NAME=tongji-robot

echo ===========================================
echo Docker 构建优化脚本
echo ===========================================
echo 镜像名称: %IMAGE_NAME%
echo 标签: %TAG%
echo ===========================================

REM 启用 BuildKit 以获得更好的构建性能
set DOCKER_BUILDKIT=1

REM 构建镜像
echo 开始构建镜像...
docker build ^
  --tag %IMAGE_NAME%:%TAG% ^
  --tag %IMAGE_NAME%:latest ^
  --build-arg BUILDKIT_INLINE_CACHE=1 ^
  --progress=plain ^
  .

if %ERRORLEVEL% neq 0 (
    echo 构建失败！
    exit /b 1
)

echo ===========================================
echo 构建完成！
echo 镜像: %IMAGE_NAME%:%TAG%
echo ===========================================

REM 显示镜像信息
docker images | findstr %IMAGE_NAME%

echo ===========================================
echo 运行测试命令:
echo docker run --rm %IMAGE_NAME%:%TAG% node --version
echo ===========================================

endlocal
