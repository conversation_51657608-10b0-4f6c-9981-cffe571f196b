FROM node:18-alpine

WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装所有依赖（包括 devDependencies 用于构建）
RUN npm ci && \
    npm cache clean --force

# 复制应用文件
COPY tsconfig.json ./
COPY agentfunc.ts ./
COPY src/ ./src/

# 构建 TypeScript
RUN npm run build

# 删除 devDependencies，只保留生产依赖
RUN npm ci --production && \
    npm cache clean --force

# 创建用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app

USER nodejs

EXPOSE 3000

ENV NODE_ENV=production
ENV WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com

CMD ["npm", "run", "start:prod"]
