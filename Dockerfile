FROM node:18-alpine

WORKDIR /app

# 复制 package.json
COPY package.json ./

# 安装依赖（包括 ts-node 和 typescript，现在在 dependencies 中）
RUN npm install && \
    npm cache clean --force

# 复制应用文件
COPY tsconfig.json ./
COPY agentfunc.ts ./
COPY src/ ./src/

# 创建用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app

USER nodejs

EXPOSE 3000

ENV NODE_ENV=production
ENV WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com

CMD ["npm", "start"]
