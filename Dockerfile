FROM node:18-alpine

WORKDIR /app

# 复制 package.json 和 package-lock.json
COPY package.json package-lock.json ./

# 安装所有依赖
RUN npm install

# 复制应用文件
COPY tsconfig.json ./
COPY agentfunc.ts ./
COPY src/ ./src/

# 构建 TypeScript
RUN npm run build

# 清理 node_modules 并只安装生产依赖
RUN rm -rf node_modules && \
    npm install --production && \
    npm cache clean --force

# 创建用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 && \
    chown -R nodejs:nodejs /app

USER nodejs

EXPOSE 3000

ENV NODE_ENV=production
ENV WECHATY_PUPPET_SERVICE_AUTHORITY=token-service-discovery-test.juzibot.com

CMD ["node", "dist/src/index.js"]
