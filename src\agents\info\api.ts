import axios from 'axios'
import { getAgentById } from '../../dao/agentDao.js'

interface ConversationResponse {
  Conversation?: {
    AppConversationID?: string
  }
}

interface ChatQueryData {
  Query: string
  AppConversationID: string
  AppKey: string
  ResponseMode: string
  UserID: string
}

export async function createConversation(sessionKey: string): Promise<string | null> {
  const agent = await getAgentById('info')
  if (!agent) return null

  const url = `${agent.base_url}/create_conversation`
  const headers = {
    'Content-Type': 'application/json',
    Apikey: agent.appkey
  }
  const data: any = {
    Appkey: agent.appid,
    UserID: '3'
  }

  // 为信息办助手添加Inputs变量
  console.log(`原始sessionKey: ${sessionKey}`)
  
  // 直接使用固定的字符串作为id
  const userId = "test_user_123"
  console.log(`使用固定userId: ${userId}`)

  // 添加Inputs变量
  const inputs = { id: userId }
  data.Inputs = inputs
  console.log('添加Inputs变量:', inputs)
  console.log('完整请求数据:', JSON.stringify(data, null, 2))

  try {
    console.log('创建信息办助手会话请求...', data)
    console.log('请求URL:', url)
    console.log('请求头:', headers)
    
    const response = await axios.post<ConversationResponse>(url, data, { headers })
    console.log('响应状态:', response.status)
    console.log('响应数据:', response.data)

    if (response.status === 200) {
      const conversationId = response.data.Conversation?.AppConversationID
      console.log(`信息办助手会话创建成功，ID: ${conversationId}`)
      if (conversationId) {
        return conversationId
      }
    } else {
      console.log(`响应状态不是200: ${response.status}`)
    }
  } catch (error: any) {
    console.error('创建信息办助手会话时发生错误:')
    console.error('错误对象:', error)
    console.error('错误响应:', error.response ? error.response.data : '无响应数据')
    console.error('错误状态:', error.response ? error.response.status : '无状态码')
    console.error('错误消息:', error.message)
  }

  return null
}

export async function* chatQueryStreaming(conversationId: string, query: string) {
  const agent = await getAgentById('info')
  if (!agent) return

  const url = `${agent.base_url}/chat_query_v2`
  const headers = {
    'Content-Type': 'application/json',
    Apikey: agent.appkey
  }
  const data: ChatQueryData = {
    Query: query,
    AppConversationID: conversationId,
    AppKey: agent.appid,
    ResponseMode: 'blocking',
    UserID: '3'
  }

  try {
    console.log('开始发送信息办助手AI请求...')
    const response = await new Promise<any>((resolve, reject) => {
      const req = axios.post(url, data, { headers })
      req.then((res) => resolve(res.data as any)).catch(reject)
    })

    yield response.answer
  } catch (error: any) {
    console.error('信息办助手请求发生错误:', error)
  }
}
